<div class="expense-allocation-tab">
  <!-- 表格区域 -->
  <div class="table-section">
    <nz-table
      #expenseTable
      [nzData]="expenseList"
      [nzLoading]="loading"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '800px', y: '400px' }"
      nzBordered
      nzSize="small">
      <thead>
        <tr>
          <th *ngFor="let column of tableColumns" [nzWidth]="column.width">
            {{ column.title }}
          </th>
        </tr>
      </thead>

      <tbody>
        <tr *ngFor="let item of expenseList; trackBy: trackByFn">
          <!-- 费用类型 -->
          <td>
            <flc-text-truncated [data]="item.type_name"></flc-text-truncated>
          </td>

          <!-- 分类 -->
          <td>
            <flc-text-truncated [data]="item.classification"></flc-text-truncated>
          </td>

          <!-- 项目 -->
          <td>
            <flc-text-truncated [data]="item.project" [nzTooltipTitle]="item.project"></flc-text-truncated>
          </td>

          <!-- 二级项目 -->
          <td>
            <flc-text-truncated [data]="item.sec_project" [nzTooltipTitle]="item.sec_project"></flc-text-truncated>
          </td>

          <!-- 三级项目 -->
          <td>
            <flc-text-truncated [data]="item.thr_project" [nzTooltipTitle]="item.thr_project"></flc-text-truncated>
          </td>

          <!-- 分摊金额 -->
          <td
            *ngIf="shouldShowField(item, 'amortization_amount')"
            [attr.rowspan]="getRowSpan(item, 'amortization_amount')"
            style="text-align: center">
            <span class="amount-value">
              <flc-text-truncated [data]="item.amortization_amount"></flc-text-truncated>
            </span>
          </td>

          <!-- 操作时间 -->
          <td *ngIf="shouldShowField(item, 'gen_time')" [attr.rowspan]="getRowSpan(item, 'gen_time')">
            <flc-text-truncated [data]="item.gen_time | date: 'yyyy-MM-dd HH:mm:ss'"></flc-text-truncated>
          </td>
        </tr>
      </tbody>
    </nz-table>

    <!-- 空数据提示 -->
    <!-- <div class="empty-data" *ngIf="!loading && expenseList.length === 0">
      <nz-empty nzNotFoundContent="暂无费用分摊信息">
        <div nz-empty-footer>
          <p class="empty-description">
            费用分摊信息来源于【费用管理】模块中操作分摊给该订单的金额
          </p>
        </div>
      </nz-empty>
    </div> -->
  </div>
</div>
