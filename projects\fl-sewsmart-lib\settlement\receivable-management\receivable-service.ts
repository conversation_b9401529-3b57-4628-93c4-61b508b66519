import { Observable, Subject, Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Injectable, EventEmitter } from '@angular/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import {
  ReceivableDetail,
  ReceivableDetailParams,
  ApiResponse,
  CustomerOption,
  AccountOption,
  OutboundList,
  OutboundListOptions,
  ReceivableSubjectEvent
} from './models/receivable-detail.interface';
import { ReceivableSubjectEventType } from './models/receivable-detail.enum';

@Injectable()
export class ReceivableService {
  private baseUrl = '/service/procurement-inventory/settlement/v1';
  userActions?: Array<string>;

  // 刷新事件发射器
  refreshEvent = new EventEmitter<any>();

  constructor(private _http: HttpClient, private _spUtil: FlcSpUtilService) {}

  // TODO 切换权限
  getUserActions() {
    if (!this.userActions && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      const actionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, []>;
      this.userActions = actionMap.get('settlement/receivable-management');
    }
    return this.userActions || [];
  }

  /* ==================== 应收单基础API ==================== */

  /**
   * 获取应收单列表
   */
  getList(data: any): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/list-bills-receivable', data);
  }

  /**
   * 获取应收单详情
   */
  getDetail(id: string): Observable<ApiResponse<ReceivableDetail>> {
    return this._http.post<any>(`${this.baseUrl}/bills-receivable-detail`, { id });
  }

  /**
   * 保存应收单
   */
  saveReceivable(data: ReceivableDetailParams): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/stash-bills-receivable`, data);
  }

  /**
   * 提交应收单
   */
  submitReceivable(data: ReceivableDetailParams): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/save-bills-receivable`, data);
  }

  /**
   * 审核应收单 返回修改  确认应收
   */
  auditReceivable(data: number[]): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/bills-receivable-audit-approve`, data);
  }

  /**
   * 生成应收单号
   */
  generateReceivableCode(): Observable<ApiResponse<{ code: string }>> {
    return this._http.post<any>(`${this.baseUrl}/bills-receivable-code`, {});
  }

  /**
   * 验证应收单号是否重复
   */
  verifyReceivableCode(code: string, id?: string | number): Observable<ApiResponse<{ success: boolean }>> {
    const payload: any = { code };
    if (id !== undefined && id !== null) {
      payload.id = id;
    }
    return this._http.post<any>(`${this.baseUrl}/bills-receivable-verify-code`, payload);
  }

  /* ==================== 基础数据API ==================== */

  /**
   * 获取客户档案选项
   */
  getCustomerOptions(keyword?: string): Observable<ApiResponse<{ data: CustomerOption[] }>> {
    return this._http.post<any>(`${this.baseUrl}/bills-receivable/list-customer`, {
      
    });
  }

  /**
   * 获取账户管理选项
   */
  getAccountOptions(): Observable<ApiResponse<{ data: AccountOption[] }>> {
    return this._http.post<any>(`${this.baseUrl}/bills-receivable/list-bank-account`, {});
  }

  /**
   * 获取币种信息
   */
  getCurrencyOptions(payload: any): Observable<ApiResponse<any>> {
    return this._http.post<any>(`/service/archive/v1/api/unit/basic_option`, payload);
  }

  /**
   * 获取结算调整项
   */
  getSettlementAdjust(): Observable<any> {
    const _payload = {
      limit: 99999,
      page: 1,
      where: [
        { column: 'settlement_type', op: '=', value: [3] },
        { column: 'status', op: '=', value: 1 },
      ],
    };
    return this._http.post<any>(`/service/archive/v1/settlement_adjust/list`,_payload);
  }

  /* ==================== 出库单选择API ==================== */

  /**
   * 获取出库单列表（用于选择）
   */
  getOutboundList(params: any): Observable<ApiResponse<{ data: OutboundList[]; total: number }>> {
    return this._http.post<any>(`${this.baseUrl}/list-bills-receivable-outbound`, params);
  }

  /**
   * 获取出库单筛选选项
   */
  getOutboundListOptions(): Observable<ApiResponse<OutboundListOptions>> {
    return this._http.post<any>(`${this.baseUrl}/list-bills-receivable-outbound-option`, {});
  }

  /* ==================== 其他API ==================== */

  /**
   * 获取列表筛选选项
   */
  getListOptions(): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/list-bills-receivable-option', {});
  }
  // 通过应收单审核
  batchPass(ids: Array<number | string>): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/bills-receivable-audit-approve', { ids });
  }
  // 驳回应收单审核
  batchReject(ids: number[], reason: string): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/bills-receivable-audit-reject', { ids, commit: reason });
  }
  // 批量开票
  batchSales(ids: number[]): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/bills-receivable/batch-sales-invoice', { ids });
  }
  // 批量转收款单
  baschToReceipt(ids: number[]) {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/receipt-bill/to-receipt', { bill_ids: ids });
  }


  /* 事件订阅相关 */
  
    private _subject = new Subject<ReceivableSubjectEvent>();
  
    private _subscriptions = new Map<string, Subscription>();
  
    addSubjectListener(key: string, [...type]: Array<ReceivableSubjectEventType>, cb: (evt: ReceivableSubjectEvent) => void) {
      if (this._subscriptions.has(key)) {
        console.warn(`the subscription with the key of ${key} is existed and will be unsubscribed first`);
        this._subscriptions.get(key)?.unsubscribe();
      }
      const subscription: Subscription = this._subject.subscribe((e) => {
        if (type.includes(e.type)) {
          cb(e);
        }
      });
      this._subscriptions.set(key, subscription);
    }
  
    removeSubjectListener(key: string) {
      this._subscriptions.get(key)?.unsubscribe();
      this._subscriptions.delete(key);
    }
  
    removeAllSubjectListener() {
      this._subscriptions.forEach((sub) => {
        sub.unsubscribe();
      });
      this._subscriptions.clear();
    }
  
    sendSubjectEvent(type: ReceivableSubjectEventType, data?: any) {
      const body: ReceivableSubjectEvent = {
        type: type,
        data: data,
      };
      this._subject.next(body);
    }
  // 批量删除应收单
  baschDelete(ids: number[]) {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/bills-receivable-delete', {ids});
  }
  
}
