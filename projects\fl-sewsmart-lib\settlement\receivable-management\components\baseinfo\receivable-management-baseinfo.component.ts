import { Component, Input, OnInit, OnDestroy, SimpleChanges, ViewChild } from '@angular/core';
import { FlcValidatorService, FlcFileGalleryComponent } from 'fl-common-lib';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PageEditModeEnum, ReceivableDetail, CustomerOption, AccountOption } from '../../models/receivable-detail.interface';
import { baseInfoConfig } from './receivable-management-baseinfo.config';
import { ReceivableService } from '../../receivable-service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ReceivableSubjectEventType } from '../../models/receivable-detail.enum';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'flss-receivable-management-baseinfo',
  templateUrl: './receivable-management-baseinfo.component.html',
  styleUrls: ['./receivable-management-baseinfo.component.scss'],
})
export class ReceivableManagementBaseinfoComponent implements OnInit, OnDestroy {
  @Input() editMode: PageEditModeEnum = PageEditModeEnum.add;
  @Input() detailInfo?: ReceivableDetail;

  constructor(
    private _fb: FormBuilder,
    private _flcValidatorService: FlcValidatorService,
    private _service: ReceivableService,
    private _noticeService: NzNotificationService
  ) {}

  baseInfoConfig = baseInfoConfig;
  pageEditModeEnum = PageEditModeEnum;
  baseInfoForm: FormGroup = this.initForm();

  // 事件订阅key
  subscribeKey = 'receivable-baseinfo';

  // 选项数据
  customerOptions: any[] = [];
  currencyOptions: any[] = [];
  accountOptions: any[] = [];

  // 订阅管理
  private codeChangeSubscription?: Subscription;

  @ViewChild('fileGalleryRef') fileGalleryRef?: FlcFileGalleryComponent;

  ngOnInit() {
    // 初始化加载所有选项数据
    this.loadInitialData();

    if (this.editMode === PageEditModeEnum.add) {
      this.getCode();
      // 设置默认税率
      this.baseInfoForm.get('invoice_tax_rate')?.setValue(13);
    }

    // 添加事件监听
    this.setupEventListeners();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.detailInfo?.currentValue) {
      this.baseInfoForm = this.initForm(this.detailInfo);

      // 如果有币种信息，设置currency字段
      if (this.detailInfo?.currency_name) {
        this.baseInfoForm.get('currency')?.setValue(this.detailInfo.currency_name);
      }
    }
  }

  ngOnDestroy(): void {
    // 清理事件订阅
    this._service.removeSubjectListener(this.subscribeKey);
  }

  /**
   * 获取客户显示名称
   */
  getCustomerDisplayName(customerId: string): string {
    if (!customerId) return '';

    const customer = this.customerOptions.find((option: any) => option.value === customerId);
    return customer?.label || '';
  }

  /**
   * 获取银行账户显示名称
   */
  getBankAccountDisplayName(bankAccountId: string): string {
    if (!bankAccountId) return '';

    const account = this.accountOptions.find((option: any) => option.value === bankAccountId);
    return account?.label || '';
  }

  private initForm(data?: ReceivableDetail) {
    const _group = this._fb.group({});
    this.baseInfoConfig.forEach((item: any) => {
      let defaultValue = data?.[item.code] ?? null;

      // 特殊字段处理
      if (data) {
        switch (item.code) {
          case 'currency':
            // 币种字段使用currency
            defaultValue = data.currency || null;
            break;
          case 'customer_id':
            // 客户ID字段
            defaultValue = data.customer_id || null;
            break;
          case 'exchange_rate':
            // 汇率字段
            defaultValue = data.exchange_rate || null;
            break;
          case 'invoice_tax_rate':
            // 税率字段
            defaultValue = data.invoice_tax_rate || null;
            break;
          case 'bank_account_id':
            // 收款账户ID字段
            defaultValue = data.bank_account_id || null;
            break;
        }
      }

      // 设置默认值
      if (!data && item.defaultValue !== undefined) {
        defaultValue = item.defaultValue;
      }

      const _control = new FormControl(defaultValue);
      item.disabled && _control.disable();
      item.required && _control.addValidators(Validators.required);

      // 为有labelCode的字段添加对应的显示字段
      if (item.labelCode) {
        let labelValue = data?.[item.labelCode] ?? null;

        // 特殊处理某些显示字段
        if (data) {
          switch (item.code) {
            case 'customer_id':
              // 客户名称需要通过客户ID从选项中获取
              labelValue = this.getCustomerDisplayName(data.customer_id || '') || null;
              break;
            case 'bank_account_id':
              // 银行账户名称需要通过账户ID从选项中获取，或使用bank_account字段
              labelValue = this.getBankAccountDisplayName(data.bank_account_id || '') || data.bank_account || null;
              break;
          }
        }

        _group.addControl(item.labelCode, new FormControl(labelValue));
      }

      _group.addControl(item.code, _control);
    });

    // 添加额外的隐藏字段用于存储ID值
    _group.addControl('currency_id', new FormControl(data?.currency_id || null));
    _group.addControl('currency_name', new FormControl(data?.currency_name || null));

    // 添加表单控件监听
    this.setupFormListeners(_group);

    return _group;
  }

  /**
   * 设置表单监听器
   */
  private setupFormListeners(formGroup: FormGroup) {
    // 监听应收单号变化，添加防抖处理
    this.codeChangeSubscription = formGroup.get('code')?.valueChanges
      .pipe(
        debounceTime(500), // 500ms防抖
        distinctUntilChanged() // 只有值真正改变时才触发
      )
      .subscribe((value: any) => {
        if (value && value.trim()) {
          this.onCodeChange(value.trim());
        }
      });

    // 监听客户选择变化
    formGroup.get('customer_id')?.valueChanges.subscribe((value) => {
      if (value) {
        this.onCustomerChange(value);
      }
    });

    // 监听币种变化
    formGroup.get('currency_id')?.valueChanges.subscribe((value) => {
      if (value) {
        this.onCurrencyChange(value);
      }
    });

    // 监听汇率变化
    formGroup.get('exchange_rate')?.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.onExchangeRateChange(value);
      }
    });

    // 监听税率变化
    formGroup.get('invoice_tax_rate')?.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.onInvoiceTaxRateChange(value);
      }
    });
  }

  /**
   * 初始化加载所有选项数据
   */
  private loadInitialData() {
    // 加载客户选项
    this.loadCustomerOptions();
    // 加载币种选项
    this.loadCurrencyOptions();
    // 加载账户选项
    this.loadAccountOptions();
  }

  /**
   * 加载客户选项
   */
  private loadCustomerOptions() {
    this._service.getCustomerOptions().subscribe({
      next: (res) => {
        if (res.code === 200 && res.data?.data) {
          this.customerOptions = res.data.data.map((item: any) => ({
            label: item.name,
            value: item.id,
            currency_id: item.currency_id,
            currency_name: item.currency_name,
            exchange_rate: item.exchange_rate,
          }));
          // 更新配置中的选项
          this.updateConfigOptions('customer_id', this.customerOptions);
        }
      },
      error: () => {
        this._noticeService.error('获取客户选项失败', '');
      },
    });
  }

  /**
   * 加载币种选项
   */
  private loadCurrencyOptions() {
    this._service
      .getCurrencyOptions({
        column: 'name',
        value: '',
        limit: 99999,
        page: 1,
        type: [9],
      })
      .subscribe({
        next: (res) => {
          if (res.code === 200 && res.data?.option_list) {
            this.currencyOptions = res.data.option_list['9'];
            // 更新配置中的选项
            this.updateConfigOptions('currency_id', this.currencyOptions);
          }
        },
        error: () => {
          this._noticeService.error('获取币种选项失败', '');
        },
      });
  }

  /**
   * 加载账户选项
   */
  private loadAccountOptions() {
    this._service.getAccountOptions().subscribe({
      next: (res) => {
        if (res.code === 200 && res.data?.data) {
          this.accountOptions = res.data.data.map((item: any) => ({
            label: `${item.deposit_bank}：${item.bank_account}`,
            value: item.id,
            bank_account: item.bank_account,
            deposit_bank: item.deposit_bank,
          }));
          console.log('accountOptions', this.accountOptions);
          // 更新配置中的选项
          this.updateConfigOptions('bank_account_id', this.accountOptions);
        }
      },
      error: () => {
        this._noticeService.error('获取收款账户选项失败', '');
      },
    });
  }

  /**
   * 更新配置中的选项
   */
  private updateConfigOptions(code: string, options: any[]) {
    const configItem = this.baseInfoConfig.find((item) => item.code === code);
    if (configItem) {
      configItem.options = options;
    }
  }

  /**
   * 获取指定字段的选项数据
   */
  getOptionsForField(code: string): any[] {
    switch (code) {
      case 'customer_id':
        return this.customerOptions;
      case 'currency_id':
        return this.currencyOptions;
      case 'bank_account_id':
        return this.accountOptions;
      default:
        // 回退到配置中的选项
        const configItem = this.baseInfoConfig.find((item) => item.code === code);
        return configItem?.options || [];
    }
  }

  private getCode() {
    this._service.generateReceivableCode().subscribe({
      next: (res: any) => {
        if (res.code === 200) {
          this.baseInfoForm.get('code')?.setValue(res.data.code);
        }
      },
      error: () => {
        this._noticeService.error('获取应收单号失败', '');
      },
    });
  }

  /**
   * 处理应收单号变化
   */
  onCodeChange(code: string) {
    // 如果是编辑模式且应收单号与原始值相同，不需要验证
    if (this.editMode === PageEditModeEnum.edit && this.detailInfo?.code === code) {
      return;
    }

    // 调用验证接口
    this._service.verifyReceivableCode(code).subscribe({
      next: (res) => {
        if (res.code === 200) {
          if (!res.data?.success) {
            // 验证失败，显示错误提示
            this._noticeService.error('应收单号重复', '请修改应收单号');
            // 设置表单控件错误状态
            const codeControl = this.baseInfoForm.get('code');
            if (codeControl) {
              codeControl.setErrors({ duplicated: true });
            }
          } else {
            // 验证成功，清除错误状态
            const codeControl = this.baseInfoForm.get('code');
            if (codeControl && codeControl.hasError('duplicated')) {
              const errors = { ...codeControl.errors };
              delete errors['duplicated'];
              codeControl.setErrors(Object.keys(errors).length > 0 ? errors : null);
            }
          }
        }
      },
      error: () => {
        this._noticeService.error('验证应收单号失败', '');
      },
    });
  }

  /**
   * 处理客户选择变化
   */
  onCustomerChange(customerId: string) {
    const selectedCustomer = this.customerOptions.find((item) => item.value === customerId);
    if (selectedCustomer) {
      // 选择客户时不自动带出币种和汇率，只设置客户名称
      // 币种和汇率将通过切换出库单来设置
    }
  }

  /**
   * 处理币种变化
   */
  onCurrencyChange(currencyId: string) {
    const selectedCurrency = this.currencyOptions.find(item => item.value === currencyId);
    const exchangeRateControl = this.baseInfoForm.get('exchange_rate');
    if (selectedCurrency) {
      exchangeRateControl?.setValidators([Validators.required]);
      exchangeRateControl?.updateValueAndValidity();
      // 如果是人民币，汇率设为1且不可修改
      if (selectedCurrency.label === '人民币' || selectedCurrency.label === 'CNY' || selectedCurrency.label === '元') {
        exchangeRateControl?.setValue(1);
        exchangeRateControl?.disable();
        // // 人民币时移除必填验证，因为值是自动设置的
        // exchangeRateControl?.clearValidators();
        // exchangeRateControl?.updateValueAndValidity();
      } else {
        // 币种非人民币时，汇率设置为空，必填，数字且4位小数
        // exchangeRateControl?.setValue("");
        exchangeRateControl?.enable();
        // // 非人民币时添加必填验证
        // exchangeRateControl?.setValidators([Validators.required]);
        // exchangeRateControl?.updateValueAndValidity();
      }
    }
  }

  /**
   * 处理汇率变化
   */
  onExchangeRateChange(exchangeRate: number) {
    // 发送汇率变化事件给 loan-detail 组件
    this._service.sendSubjectEvent(ReceivableSubjectEventType.exchangeRate, {
      exchange_rate: exchangeRate,
    });
  }

  /**
   * 处理税率变化
   */
  onInvoiceTaxRateChange(taxRate: number) {
    // 发送税率变化事件给 loan-detail 组件
    this._service.sendSubjectEvent(ReceivableSubjectEventType.invoiceTaxRate, {
      invoice_tax_rate: taxRate,
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    this._service.addSubjectListener(
      this.subscribeKey,
      [ReceivableSubjectEventType.customerInfo, ReceivableSubjectEventType.currencyInfo],
      (res) => {
        if (res.type === ReceivableSubjectEventType.customerInfo) {
          // 处理客户信息变更事件
          this.onCustomerInfoChange(res.data);
        } else if (res.type === ReceivableSubjectEventType.currencyInfo) {
          // 处理币种信息变更事件
          this.onCurrencyInfoChange(res.data);
        }
      }
    );
  }

  /**
   * 处理客户信息变更事件
   * @param customerData 客户数据
   */
  private onCustomerInfoChange(customerData: { customer_id: string; customer_name: string }) {
    if (customerData?.customer_id && customerData?.customer_name) {
      // 更新表单中的客户信息
      this.baseInfoForm.get('customer_id')?.setValue(customerData.customer_id);
      this.baseInfoForm.get('customer_name')?.setValue(customerData.customer_name);

      // 不再自动触发客户选择变化的逻辑，因为选择客户不需要带出币种和汇率
    }
  }

  /**
   * 处理币种信息变更事件
   * @param currencyData 币种数据
   */
  private onCurrencyInfoChange(currencyData: { currency_id: string; currency_name: string }) {
    if (currencyData?.currency_id && currencyData?.currency_name) {
      // 更新表单中的币种信息
      this.baseInfoForm.get('currency_id')?.setValue(currencyData.currency_id);
      this.baseInfoForm.get('currency_name')?.setValue(currencyData.currency_name);
      this.baseInfoForm.get('currency')?.setValue(currencyData.currency_name);

      // 触发币种选择变化的逻辑（设置汇率）
      this.onCurrencyChange(currencyData.currency_id);
    }
  }

  /**
   * 处理选择框变化
   */
  onSelectChange(value: any, item: any) {
    if (item.code === 'customer_id') {
      // 客户选择变化时，同时设置客户名称
      const selectedCustomer = this.customerOptions.find((opt) => opt.value === value);
      if (selectedCustomer) {
        this.baseInfoForm.get('customer_name')?.setValue(selectedCustomer.label);
      }
    } else if (item.code === 'currency_id') {
      // 币种选择变化时，同时设置币种名称
      const selectedCurrency = this.currencyOptions.find((opt) => opt.value === value);
      if (selectedCurrency) {
        this.baseInfoForm.get('currency_name')?.setValue(selectedCurrency.label);
      }
    } else if (item.code === 'bank_account_id') {
      // 收款账户选择变化时，同时设置显示名称
      const selectedAccount = this.accountOptions.find((opt) => opt.value === value);
      if (selectedAccount) {
        this.baseInfoForm.get('bank_account_display')?.setValue(selectedAccount.label);
      }
    }
  }

  /**
   * 附件上传完成事件
   * @param fileList 上传的文件列表
   * @param item 配置项
   */
  handleUpload(fileList: any, item: any) {
    // 只取第一个文件的URL作为字符串存储
    const fileUrl = fileList && fileList.length > 0 ? fileList[0].url : null;
    this.baseInfoForm.get(item.attachment)?.setValue(fileUrl);
    this.baseInfoForm.get(item.attachment)?.markAsDirty();
  }

  /**
   * 附件删除事件
   * @param fileList 删除后的文件列表
   * @param item 配置项
   */
  handleDeleted(fileList: any, item: any) {
    // 如果文件列表为空，设置为null；否则取第一个文件的URL
    const fileUrl = fileList && fileList.length > 0 ? fileList[0].url : null;
    this.baseInfoForm.get(item.attachment)?.setValue(fileUrl);
    this.baseInfoForm.get(item.attachment)?.markAsDirty();
  }

  /**
   * 检查文件是否正在上传
   * @returns 是否可以继续操作
   */
  checkFileUploading(): boolean {
    if (this.fileGalleryRef?.status === 'uploading') {
      this._noticeService.error('请等待附件上传完成', '');
      return false;
    }
    return true;
  }

  /**
   * 获取用于显示的文件列表
   * @param fieldName 字段名
   * @returns 文件列表数组
   */
  getFileListForDisplay(fieldName: string): any[] {
    const fileUrl = this.baseInfoForm.get(fieldName)?.value;
    if (!fileUrl) {
      return [];
    }

    // 如果是字符串URL，转换为文件对象格式
    if (typeof fileUrl === 'string') {
      return [
        {
          url: fileUrl,
          name: this.getFileNameFromUrl(fileUrl),
          status: 'done',
        },
      ];
    }

    // 如果已经是数组格式，直接返回（兼容性处理）
    if (Array.isArray(fileUrl)) {
      return fileUrl.slice(0, 1); // 只取第一个文件
    }

    return [];
  }

  /**
   * 从URL中提取文件名
   * @param url 文件URL
   * @returns 文件名
   */
  private getFileNameFromUrl(url: string): string {
    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];
      return fileName || '附件';
    } catch {
      return '附件';
    }
  }

  handleDynamicSearch(event: any, item: any) {
    // 处理动态搜索逻辑
    console.log('Dynamic search:', event, item);
  }

  /**
   * 表单验证
   * @returns 是否有效
   */
  formInvalid(): boolean {
    // 检查附件上传状态
    if (!this.checkFileUploading()) {
      return true;
    }

    // 检查应收单号是否重复
    const codeControl = this.baseInfoForm.get('code');
    if (codeControl && codeControl.hasError('duplicated')) {
      this._noticeService.error('应收单号重复', '请修改应收单号');
      return true;
    }

    // 检查表单验证
    if (this._flcValidatorService.formIsInvalid(this.baseInfoForm)) {
      this._noticeService.error('请检查必填项', '');
      return true;
    }

    return false;
  }

  getFormValue() {
    const _value = this.baseInfoForm.getRawValue();
    console.log('getFormValue - 基础信息表单值:', {
      rawValue: _value,
      exchange_rate: _value.exchange_rate,
      exchange_rate_control_disabled: this.baseInfoForm.get('exchange_rate')?.disabled,
      exchange_rate_control_value: this.baseInfoForm.get('exchange_rate')?.value,
    });

    return {
      code: _value.code,
      customer_id: _value.customer_id,
      customer_name: _value.customer_name,
      currency_id: _value.currency_id,
      currency_name: _value.currency_name,
      currency: _value.currency, // 添加currency字段
      exchange_rate: String(_value.exchange_rate),
      invoice_tax_rate: _value.invoice_tax_rate,
      bank_account_id: _value.bank_account_id,
      bank_account_display: _value.bank_account_display,
      attachment: _value.attachment,
    };
  }
}
