import { Component, Input, OnInit, OnDestroy, OnChanges, SimpleChanges } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CostBreakdownService } from '../../cost-breakdown.service';
import { ExpenseAllocation, ExpenseAllocationResponse } from '../../models';

@Component({
  selector: 'flss-expense-allocation-tab',
  templateUrl: './expense-allocation-tab.component.html',
  styleUrls: ['./expense-allocation-tab.component.scss'],
})
export class ExpenseAllocationTabComponent implements OnInit, OnDestroy, OnChanges {
  @Input() orderId!: string;

  private destroy$ = new Subject<void>();

  // 数据
  loading = false;
  expenseList: (ExpenseAllocation & { _rowSpan?: number; _showRow?: boolean })[] = [];

  // 表头配置
  tableColumns = [
    { title: '类型', key: 'type_name', width: '120px' },
    { title: '分类', key: 'classification', width: '120px' },
    { title: '项目', key: 'project', width: '140px' },
    { title: '二级项目', key: 'sec_project', width: '140px' },
    { title: '三级项目', key: 'thr_project', width: '140px' },
    { title: '分摊金额', key: 'amortization_amount', width: '120px', align: 'right' },
    { title: '操作时间', key: 'gen_time', width: '150px' },
  ];

  // 合计金额
  totalAmount = 0;

  constructor(private message: NzMessageService, private costBreakdownService: CostBreakdownService) {}

  ngOnInit() {
    if (this.orderId) {
      this.loadExpenseAllocation();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    // 监听orderId变化（包括从一个UUID切换到另一个UUID）
    if (changes['orderId']) {
      const currentValue = changes['orderId'].currentValue;
      const previousValue = changes['orderId'].previousValue;

      // 如果不是首次变化，且orderId确实发生了变化
      if (!changes['orderId'].firstChange && currentValue !== previousValue) {
        console.log('订单UUID变化，重新加载费用分摊', {
          from: previousValue,
          to: currentValue,
        });
        this.loadExpenseAllocation();
      }
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 加载费用分摊信息
   */
  private loadExpenseAllocation() {
    this.loading = true;
    this.costBreakdownService
      .getExpenseAllocation(this.orderId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          console.log('费用分摊API响应:', response);

          // 确保数据是数组格式
          if (Array.isArray(response)) {
            this.expenseList = response;
          } else if (response && Array.isArray(response.data)) {
            this.expenseList = response.data;
          } else if (response && response.data && Array.isArray(response.data.data)) {
            this.expenseList = response.data.data;
          } else {
            console.warn('费用分摊数据格式不正确:', response);
            this.expenseList = [];
          }

          // 处理数据，添加合并信息
          this.processExpenseListForMerging();
          console.log('处理后的费用分摊列表:', this.expenseList);
          this.loading = false;
        },
        error: (error: any) => {
          this.message.error('获取费用分摊信息失败');
          this.loading = false;
          console.error('获取费用分摊信息失败:', error);
        },
      });
  }

  /**
   * 计算合计金额
   */
  private calculateTotal() {
    this.totalAmount = this.expenseList.reduce((sum, item) => {
      const amount = parseFloat(item.amortization_amount) || 0;
      return sum + amount;
    }, 0);
  }

  /**
   * 获取字段值
   */
  getFieldValue(item: any, key: string): string {
    const value = item[key];
    return value !== null && value !== undefined ? value : '-';
  }

  /**
   * trackBy函数用于优化ngFor性能
   */
  trackByFn(index: number, item: any): any {
    return item.id || index;
  }

  /**
   * 处理费用分摊列表，添加合并信息
   */
  private processExpenseListForMerging() {
    if (!this.expenseList || this.expenseList.length === 0) {
      return;
    }

    // 为每个项目添加合并信息
    this.expenseList.forEach((item: any) => {
      item._rowSpan = 1;
      item._showRow = true;
    });

    // 计算需要合并的行
    for (let i = 0; i < this.expenseList.length; i++) {
      const currentItem = this.expenseList[i];

      if (!currentItem._showRow) {
        continue;
      }

      let mergeCount = 1;

      // 向下查找相同的分摊金额和操作时间
      for (let j = i + 1; j < this.expenseList.length; j++) {
        const nextItem = this.expenseList[j];

        if (this.shouldMergeRows(currentItem, nextItem)) {
          nextItem._showRow = false; // 隐藏后续相同的行
          mergeCount++;
        } else {
          break; // 遇到不同的数据就停止
        }
      }

      currentItem._rowSpan = mergeCount;
    }
  }

  /**
   * 判断两行是否应该合并
   */
  private shouldMergeRows(item1: any, item2: any): boolean {
    return item1.amortization_amount === item2.amortization_amount && item1.gen_time === item2.gen_time;
  }

  /**
   * 获取行的rowSpan值
   */
  getRowSpan(item: any, field: string): number {
    if (field === 'amortization_amount' || field === 'gen_time') {
      return item._showRow ? item._rowSpan : 0;
    }
    return 1;
  }

  /**
   * 判断是否显示该行的指定字段
   */
  shouldShowField(item: any, field: string): boolean {
    if (field === 'amortization_amount' || field === 'gen_time') {
      return item._showRow;
    }
    return true;
  }
}
